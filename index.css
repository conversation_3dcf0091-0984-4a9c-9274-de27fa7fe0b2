/* Modern Design System CSS */

/* Base Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', system-ui, sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Modern Focus States */
*:focus {
  outline: 2px solid theme('colors.crimson-accent-blue');
  outline-offset: 2px;
  border-radius: 0.375rem;
}

*:focus:not(:focus-visible) {
  outline: none;
}

/* Smooth Transitions */
.transition-modern {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Modern Button Hover States */
.btn-modern {
  @apply transition-modern transform;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
}

.btn-modern:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px 0 rgb(0 0 0 / 0.15);
}

.btn-modern:active {
  transform: translateY(0);
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
}

/* Status Indicator Classes */
.status-success {
  @apply bg-status-success-light text-status-success-dark border border-status-success;
}

.status-warning {
  @apply bg-status-warning-light text-status-warning-dark border border-status-warning;
}

.status-error {
  @apply bg-status-error-light text-status-error-dark border border-status-error;
}

.status-info {
  @apply bg-status-info-light text-status-info-dark border border-status-info;
}

/* Progress Bar Styles */
.progress-success {
  @apply bg-status-success;
}

.progress-warning {
  @apply bg-status-warning;
}

.progress-error {
  @apply bg-status-error;
}

.progress-info {
  @apply bg-status-info;
}

/* Typography Hierarchy */
.text-hierarchy-1 {
  @apply text-2xl font-bold text-text-primary;
  line-height: 1.3;
  letter-spacing: -0.025em;
}

.text-hierarchy-2 {
  @apply text-xl font-semibold text-text-primary;
  line-height: 1.4;
  letter-spacing: -0.015em;
}

.text-hierarchy-3 {
  @apply text-lg font-semibold text-text-primary;
  line-height: 1.5;
}

.text-hierarchy-4 {
  @apply text-base font-medium text-text-primary;
  line-height: 1.5;
}

.text-hierarchy-5 {
  @apply text-sm font-medium text-text-primary;
  line-height: 1.6;
}

.text-hierarchy-6 {
  @apply text-xs font-normal text-text-secondary;
  line-height: 1.6;
}

/* Modern Card Styles */
.card-modern {
  @apply bg-white border border-gray-200 rounded-xl shadow-soft transition-modern;
}

.card-modern:hover {
  @apply shadow-medium border-gray-300;
}

/* Interactive Elements */
.interactive-element {
  @apply transition-modern cursor-pointer;
}

.interactive-element:hover {
  @apply scale-105;
}

.interactive-element:active {
  @apply scale-95;
}

/* Grid Alignment Utilities */
.grid-aligned {
  @apply grid gap-3;
  align-items: start;
}

@media (min-width: 768px) {
  .grid-aligned {
    @apply gap-4;
  }
}

@media (min-width: 1024px) {
  .grid-aligned {
    @apply gap-6;
  }
}

/* Priority Indicators */
.priority-urgent {
  @apply status-error animate-pulse;
}

.priority-high {
  @apply status-warning;
}

.priority-medium {
  @apply status-info;
}

.priority-low {
  @apply bg-gray-100 text-gray-600 border border-gray-300;
}

/* Confidence Score Colors */
.confidence-high {
  @apply text-status-success-dark font-semibold;
}

.confidence-medium {
  @apply text-status-info-dark font-medium;
}

.confidence-low {
  @apply text-status-warning-dark font-medium;
}

/* Modern Input Styles */
.input-modern {
  @apply border border-gray-300 rounded-lg px-3 py-2 text-sm transition-modern;
  @apply focus:border-crimson-blue focus:ring-2 focus:ring-crimson-blue focus:ring-opacity-20;
}

/* Loading States */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Responsive Typography */
@media (max-width: 640px) {
  .text-hierarchy-1 {
    @apply text-xl;
  }
  
  .text-hierarchy-2 {
    @apply text-lg;
  }
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded-full;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}

/* Notification Styles */
.notification-success {
  @apply bg-status-success-light border-l-4 border-status-success text-status-success-dark;
}

.notification-warning {
  @apply bg-status-warning-light border-l-4 border-status-warning text-status-warning-dark;
}

.notification-error {
  @apply bg-status-error-light border-l-4 border-status-error text-status-error-dark;
}

.notification-info {
  @apply bg-status-info-light border-l-4 border-status-info text-status-info-dark;
}
