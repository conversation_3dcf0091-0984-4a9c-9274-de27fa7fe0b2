import { Donor } from '../types';

export const mockDonorProfiles: Record<string, Donor> = {
  'sarah-j': {
    id: 'sarah-j',
    name: '<PERSON>',
    photoUrl: 'https://i.pravatar.cc/150?u=sarah-johnson',
    email: '<EMAIL>',
    phone: '(*************',
    address: '123 Oak Street, Springfield, IL 62701',
    contactInfo: {
      home: '(*************',
      work: '(*************',
      email: '<EMAIL>',
      website: 'linkedin.com/in/sarah<PERSON><PERSON>son'
    },
    aiBadges: ['Major <PERSON><PERSON>', 'Consistent Giver', 'High Engagement'],
    predictiveAsk: 350,
    recurrencePrediction: 'Very Likely (85%)',
    suggestedAction: 'Personal thank you call within 48 hours',
    givingOverview: {
      totalRaised: 2850,
      consecutiveGifts: 6,
      tier: 'Gold Supporter',
      topGifts: [
        { name: 'Q1 2024', value: 250 },
        { name: 'Q4 2023', value: 300 },
        { name: 'Q3 2023', value: 200 },
        { name: 'Q2 2023', value: 150 },
        { name: 'Q1 2023', value: 175 }
      ]
    },
    aiSnapshot: 'Sarah is a dedicated supporter who has shown consistent growth in her giving pattern. She responds well to email communications and has a strong interest in education policy. Her giving frequency suggests she prefers quarterly contributions. Recent engagement data shows high email open rates and website visits during campaign updates.'
  },
  
  'michael-r': {
    id: 'michael-r',
    name: '<PERSON> <PERSON>',
    photoUrl: 'https://i.pravatar.cc/150?u=michael-rodriguez',
    email: '<EMAIL>',
    phone: '(*************',
    address: '456 Pine Avenue, Chicago, IL 60601',
    contactInfo: {
      home: '(*************',
      work: '(*************',
      email: '<EMAIL>',
      website: 'twitter.com/mrodriguez'
    },
    aiBadges: ['Phone Responder', 'Event Attendee', 'Volunteer'],
    predictiveAsk: 125,
    recurrencePrediction: 'Likely (72%)',
    suggestedAction: 'Invite to upcoming town hall event',
    givingOverview: {
      totalRaised: 850,
      consecutiveGifts: 4,
      tier: 'Silver Supporter',
      topGifts: [
        { name: 'Dec 2023', value: 100 },
        { name: 'Oct 2023', value: 75 },
        { name: 'Aug 2023', value: 125 },
        { name: 'Jun 2023', value: 50 },
        { name: 'Apr 2023', value: 100 }
      ]
    },
    aiSnapshot: 'Michael is an engaged community member who prefers phone contact over email. He has attended 3 campaign events and volunteers occasionally. His giving pattern shows responsiveness to direct asks during events. He works in healthcare and is particularly interested in healthcare policy initiatives.'
  },

  'jennifer-l': {
    id: 'jennifer-l',
    name: 'Jennifer Liu',
    photoUrl: 'https://i.pravatar.cc/150?u=jennifer-liu',
    email: '<EMAIL>',
    phone: '(*************',
    address: '789 Maple Drive, Austin, TX 78701',
    contactInfo: {
      home: '(*************',
      work: '(*************',
      email: '<EMAIL>',
      website: 'linkedin.com/in/jenniferliu'
    },
    aiBadges: ['Major Donor', 'Tech Professional', 'Monthly Giver'],
    predictiveAsk: 750,
    recurrencePrediction: 'Very Likely (91%)',
    suggestedAction: 'Upgrade to monthly sustaining donor program',
    givingOverview: {
      totalRaised: 4200,
      consecutiveGifts: 8,
      tier: 'Platinum Supporter',
      topGifts: [
        { name: 'Jan 2024', value: 500 },
        { name: 'Nov 2023', value: 600 },
        { name: 'Sep 2023', value: 400 },
        { name: 'Jul 2023', value: 350 },
        { name: 'May 2023', value: 500 }
      ]
    },
    aiSnapshot: 'Jennifer is a high-capacity donor working in tech with strong progressive values. She prefers digital communication and has set up automatic monthly donations. Her giving shows significant capacity for major gift solicitation. She is particularly passionate about climate change and technology policy.'
  },

  'david-k': {
    id: 'david-k',
    name: 'David Kim',
    photoUrl: 'https://i.pravatar.cc/150?u=david-kim',
    email: '<EMAIL>',
    phone: '(*************',
    address: '321 Cedar Lane, Seattle, WA 98101',
    contactInfo: {
      home: '(*************',
      work: '(*************',
      email: '<EMAIL>',
      website: 'facebook.com/davidkim'
    },
    aiBadges: ['New Donor', 'Social Media Engaged', 'Young Professional'],
    predictiveAsk: 100,
    recurrencePrediction: 'Moderate (58%)',
    suggestedAction: 'Send welcome series and policy updates',
    givingOverview: {
      totalRaised: 225,
      consecutiveGifts: 2,
      tier: 'Bronze Supporter',
      topGifts: [
        { name: 'Feb 2024', value: 75 },
        { name: 'Jan 2024', value: 50 },
        { name: 'Dec 2023', value: 100 }
      ]
    },
    aiSnapshot: 'David is a new supporter who discovered the campaign through social media. He is a young professional in his late 20s with growing political engagement. His initial gifts show promise for cultivation into a regular donor. He is active on social platforms and shares campaign content frequently.'
  },

  'lisa-m': {
    id: 'lisa-m',
    name: 'Lisa Martinez',
    photoUrl: 'https://i.pravatar.cc/150?u=lisa-martinez',
    email: '<EMAIL>',
    phone: '(*************',
    address: '654 Birch Street, Denver, CO 80201',
    contactInfo: {
      home: '(*************',
      work: '(*************',
      email: '<EMAIL>',
      website: 'instagram.com/lisamartinez'
    },
    aiBadges: ['Lapsed Donor', 'High Capacity', 'Reactivation Target'],
    predictiveAsk: 1200,
    recurrencePrediction: 'Uncertain (45%)',
    suggestedAction: 'Personal outreach with campaign update',
    givingOverview: {
      totalRaised: 3500,
      consecutiveGifts: 0,
      tier: 'Former Major Donor',
      topGifts: [
        { name: 'Mar 2023', value: 1000 },
        { name: 'Jan 2023', value: 500 },
        { name: 'Nov 2022', value: 750 },
        { name: 'Sep 2022', value: 600 },
        { name: 'Jul 2022', value: 650 }
      ]
    },
    aiSnapshot: 'Lisa was a major donor who has not contributed in over 10 months. She has high capacity and previous strong engagement. Her lapse may be due to communication preferences or policy concerns. Reactivation efforts should focus on personal touch and addressing any potential concerns about campaign direction.'
  }
};

// Helper function to get donor profile by name
export const getDonorProfileByName = (name: string): Donor | null => {
  // Create a mapping of display names to profile keys
  const nameMapping: Record<string, string> = {
    'Sarah J.': 'sarah-j',
    'Sarah Johnson': 'sarah-j',
    'Michael R.': 'michael-r',
    'Michael Rodriguez': 'michael-r',
    'Jennifer L.': 'jennifer-l',
    'Jennifer Liu': 'jennifer-l',
    'David K.': 'david-k',
    'David Kim': 'david-k',
    'Lisa M.': 'lisa-m',
    'Lisa Martinez': 'lisa-m',
    'Joseph M. Banks': 'sarah-j', // Map Joseph Banks to Sarah for now
    'Joseph Banks': 'sarah-j'
  };

  const profileKey = nameMapping[name];
  return profileKey ? mockDonorProfiles[profileKey] : null;
};
