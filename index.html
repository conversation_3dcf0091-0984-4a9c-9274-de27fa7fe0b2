
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Crimson AI CRM</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              // Primary Brand Colors
              'crimson-red': '#ef4737',
              'crimson-blue': '#2f7fc3',
              'crimson-dark-blue': '#1e5a8a',
              'crimson-accent-blue': '#06c3f6',

              // Status Colors - Modern & Consistent
              'status-success': '#10b981',
              'status-success-light': '#d1fae5',
              'status-success-dark': '#047857',

              'status-warning': '#f59e0b',
              'status-warning-light': '#fef3c7',
              'status-warning-dark': '#d97706',

              'status-error': '#ef4444',
              'status-error-light': '#fee2e2',
              'status-error-dark': '#dc2626',

              'status-info': '#3b82f6',
              'status-info-light': '#dbeafe',
              'status-info-dark': '#1d4ed8',

              // Base Colors
              'base-100': '#ffffff',
              'base-200': '#f9fafb',
              'base-300': '#f3f4f6',
              'text-primary': '#111827',
              'text-secondary': '#6b7280',
              'text-tertiary': '#9ca3af',
            },
            fontFamily: {
              'sans': ['Inter', 'system-ui', 'sans-serif'],
            },
            fontSize: {
              'xs': ['0.75rem', { lineHeight: '1.6' }],
              'sm': ['0.875rem', { lineHeight: '1.6' }],
              'base': ['1rem', { lineHeight: '1.5' }],
              'lg': ['1.125rem', { lineHeight: '1.5' }],
              'xl': ['1.25rem', { lineHeight: '1.4' }],
              '2xl': ['1.5rem', { lineHeight: '1.3' }],
            },
            spacing: {
              '18': '4.5rem',
              '88': '22rem',
            },
            borderRadius: {
              'xl': '1rem',
              '2xl': '1.5rem',
            },
            boxShadow: {
              'soft': '0 2px 8px 0 rgb(0 0 0 / 0.08)',
              'medium': '0 4px 12px 0 rgb(0 0 0 / 0.12)',
              'strong': '0 8px 24px 0 rgb(0 0 0 / 0.16)',
            },
            animation: {
              'fade-in': 'fadeIn 0.3s ease-in-out',
              'slide-up': 'slideUp 0.3s ease-out',
              'scale-in': 'scaleIn 0.2s ease-out',
            },
            keyframes: {
              fadeIn: {
                '0%': { opacity: '0' },
                '100%': { opacity: '1' },
              },
              slideUp: {
                '0%': { transform: 'translateY(10px)', opacity: '0' },
                '100%': { transform: 'translateY(0)', opacity: '1' },
              },
              scaleIn: {
                '0%': { transform: 'scale(0.95)', opacity: '0' },
                '100%': { transform: 'scale(1)', opacity: '1' },
              },
            },
          }
        }
      }
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
    </style>
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@18.2.0",
    "react-dom/client": "https://esm.sh/react-dom@18.2.0/client",
    "recharts": "https://esm.sh/recharts@^2.12.7",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
<body>
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
</body>
</html>