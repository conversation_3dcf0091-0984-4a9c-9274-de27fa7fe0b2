import React, { useState } from 'react';
import { Donor } from '../../types';
import Card from '../ui/Card';
import Button from '../ui/Button';
import Badge from '../ui/Badge';
import {
  SparklesIcon,
  MailIcon,
  PhoneIcon,
  CalendarIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  FireIcon,
  TrendingUpIcon,
  UserGroupIcon,
  ChartBarIcon,
  BoltIcon,
  EyeIcon,
  HeartIcon,
  TrophyIcon,
  ComputerDesktopIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  ChatBubbleLeftRightIcon,
  BellIcon,
  StarIcon,
  UserIcon,
  BrainIcon
} from '../../constants';
import { BarChart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell, CartesianGrid } from 'recharts';

interface EnhancedDonorProfileProps {
  donor: Donor;
}

const AIInsightCard: React.FC<{
  title: string;
  insight: string;
  confidence: number;
  action?: string;
  icon: React.ReactNode;
  color: string;
}> = ({ title, insight, confidence, action, icon, color }) => (
  <div className={`bg-gradient-to-r ${color} rounded-xl p-4 border shadow-sm hover:shadow-md transition-all duration-300`}>
    <div className="flex items-start gap-3">
      <div className="bg-white p-2 rounded-lg shadow-sm">
        {icon}
      </div>
      <div className="flex-1">
        <div className="flex items-center justify-between mb-2">
          <h4 className="font-bold text-gray-900 text-sm">{title}</h4>
          <div className="flex items-center gap-1">
            <StarIcon className="w-3 h-3 text-yellow-500" />
            <span className="text-xs font-semibold text-gray-700">{confidence}%</span>
          </div>
        </div>
        <p className="text-sm text-gray-700 mb-3 leading-relaxed">{insight}</p>
        {action && (
          <Button variant="secondary" size="xs" className="font-medium">
            {action}
          </Button>
        )}
      </div>
    </div>
  </div>
);

const QuickActionButton: React.FC<{
  icon: React.ReactNode;
  label: string;
  onClick: () => void;
  variant?: 'primary' | 'secondary' | 'success' | 'warning';
  badge?: string;
}> = ({ icon, label, onClick, variant = 'secondary', badge }) => (
  <div className="relative">
    <Button
      variant={variant}
      size="sm"
      onClick={onClick}
      className="flex items-center gap-2 w-full justify-center font-medium"
    >
      {icon}
      {label}
    </Button>
    {badge && (
      <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold">
        {badge}
      </div>
    )}
  </div>
);

const MetricCard: React.FC<{
  label: string;
  value: string | number;
  trend?: 'up' | 'down' | 'stable';
  color?: string;
  subtitle?: string;
}> = ({ label, value, trend, color = 'text-gray-900', subtitle }) => (
  <div className="text-center p-4 bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
    <div className="flex items-center justify-center gap-1 mb-1">
      <p className={`text-2xl font-bold ${color}`}>{value}</p>
      {trend && (
        <TrendingUpIcon className={`w-4 h-4 ${
          trend === 'up' ? 'text-green-500' : 
          trend === 'down' ? 'text-red-500 rotate-180' : 
          'text-gray-400'
        }`} />
      )}
    </div>
    <p className="text-sm font-medium text-gray-600">{label}</p>
    {subtitle && (
      <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
    )}
  </div>
);

const EnhancedDonorProfile: React.FC<EnhancedDonorProfileProps> = ({ donor }) => {
  const [activeSection, setActiveSection] = useState<'overview' | 'intelligence' | 'engagement' | 'actions'>('overview');

  // AI Snapshot for political fundraising context
  const aiSnapshot = `${donor.name.split(' ')[0]} is a cornerstone supporter and community leader in the Neighborhood MVPs segment. He has consistently increased his giving over the past 3 years and serves as an informal campaign ambassador in his community. His recent $5,000 gift demonstrates strong commitment. He prefers in-person meetings and responds well to policy briefings. Excellent candidate for campaign advisory role.`;

  // Political fundraising tier levels
  const tierLevel = "Major Donor - Eagle Circle";
  const tierBenefits = ["Quarterly briefings with candidate", "VIP event access", "Policy preview calls"];

  // Contact Intelligence for political context
  const contactIntelligence = {
    preferredMethod: "In-person meetings",
    bestTimes: "Tuesday-Thursday, 2-4 PM",
    responseRate: "94%",
    avgResponseTime: "2.3 hours",
    lastContact: "12 days ago",
    nextScheduled: "Policy briefing - Jan 25"
  };

  // Giving Intelligence with political focus
  const givingIntelligence = {
    totalLifetime: "$47,500",
    thisElectionCycle: "$12,500",
    averageGift: "$850",
    givingTrend: "+40% vs last cycle",
    peakMonths: "March, October",
    motivations: ["Policy alignment", "Candidate access", "Community impact"]
  };

  // Digital engagement tracking
  const digitalEngagement = {
    emailOpen: "87%",
    emailClick: "34%",
    websiteVisits: "23 this month",
    socialMedia: "Active on LinkedIn, Twitter",
    volunteerHours: "45 hours this cycle",
    eventAttendance: "8 of 10 events"
  };

  // Next Best Actions for political fundraising
  const nextBestActions = [
    {
      action: "Schedule Policy Briefing",
      priority: "High",
      confidence: "92%",
      expectedOutcome: "$2,500 ask",
      timeframe: "Next 2 weeks",
      reason: "Responds well to policy discussions and due for major ask"
    },
    {
      action: "Invite to Advisory Committee",
      priority: "Medium",
      confidence: "78%",
      expectedOutcome: "Increased engagement",
      timeframe: "Next month",
      reason: "Leadership potential and community influence"
    },
    {
      action: "Host House Party Ask",
      priority: "High",
      confidence: "85%",
      expectedOutcome: "$5,000 + network",
      timeframe: "Next 6 weeks",
      reason: "Previous host success and strong network"
    }
  ];

  // Mock giving history data
  const givingHistory = [
    { month: 'Jan', amount: 250, interactions: 3 },
    { month: 'Feb', amount: 0, interactions: 1 },
    { month: 'Mar', amount: 500, interactions: 4 },
    { month: 'Apr', amount: 250, interactions: 2 },
    { month: 'May', amount: 750, interactions: 5 },
    { month: 'Jun', amount: 0, interactions: 1 }
  ];

  const handleQuickCall = () => {
    alert(`📞 Calling ${donor.name}\n\nAI Suggested Script:\n"Hi ${donor.name.split(' ')[0]}, this is Sofia from the campaign. I hope you're doing well! I wanted to follow up on our conversation about the upcoming election. Based on your previous support, I think you'd be interested in our new voter outreach initiative..."\n\nSuggested ask: $${donor.predictiveAsk.toLocaleString()}`);
  };

  const handleQuickEmail = () => {
    alert(`✉️ AI-Generated Email Draft:\n\nSubject: Your impact in action - see what your support accomplished\n\nHi ${donor.name.split(' ')[0]},\n\nI hope this finds you well! I wanted to share some exciting updates on how your generous support has been making a real difference...\n\n[Personalized content based on donor interests and giving history]\n\nBest regards,\nSofia`);
  };

  const handleScheduleMeeting = () => {
    alert(`📅 Smart Scheduling Assistant:\n\nBest times for ${donor.name}:\n• Tuesday, 2:00 PM - 4:00 PM (87% response rate)\n• Wednesday, 10:00 AM - 12:00 PM (82% response rate)\n• Thursday, 3:00 PM - 5:00 PM (79% response rate)\n\nPreferred location: Coffee meeting (based on past preferences)\nSuggested agenda: Discuss upcoming initiatives, thank for past support, explore increased engagement`);
  };

  return (
    <div className="space-y-6">
      {/* Enhanced Header with Profile Image and Quick Actions */}
      <Card className="overflow-hidden">
        <div className="bg-gradient-to-r from-crimson-blue to-blue-600 text-white p-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Profile Info with Image */}
            <div className="flex items-center gap-4">
              <div className="relative">
                <img
                  src={donor.photoUrl}
                  alt={donor.name}
                  className="w-20 h-20 rounded-full ring-4 ring-white/30 shadow-lg"
                />
                {donor.urgencyIndicators?.isHotLead && (
                  <div className="absolute -top-1 -right-1 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center shadow-lg">
                    <FireIcon className="w-3 h-3 text-white" />
                  </div>
                )}
                <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 bg-green-500 text-white text-xs px-2 py-1 rounded-full font-bold">
                  {donor.givingIntelligence?.capacityScore || 85}%
                </div>
              </div>
              <div>
                <h2 className="text-xl font-bold">{donor.name}</h2>
                <p className="text-blue-100 text-sm">{tierLevel}</p>
                <div className="flex flex-wrap gap-1 mt-2">
                  {donor.aiBadges.slice(0, 2).map(badge => (
                    <Badge key={badge} className="bg-white/20 text-white border-white/30 text-xs">
                      {badge}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            {/* Key Metrics */}
            <div className="grid grid-cols-2 gap-4">
              <MetricCard
                label="This Cycle"
                value={givingIntelligence.thisElectionCycle}
                color="text-white"
                subtitle={givingIntelligence.givingTrend}
              />
              <MetricCard
                label="Response Rate"
                value={contactIntelligence.responseRate}
                color="text-white"
                subtitle="vs peers"
              />
              <MetricCard
                label="Engagement"
                value="94/100"
                color="text-white"
                subtitle="Political score"
              />
              <MetricCard
                label="Next Ask"
                value="$750"
                color="text-white"
                subtitle="AI suggested"
              />
            </div>

            {/* Quick Actions */}
            <div className="space-y-3">
              <h3 className="font-semibold text-white/90 text-sm">Quick Actions</h3>
              <div className="grid grid-cols-2 gap-2">
                <QuickActionButton
                  icon={<PhoneIcon className="w-4 h-4" />}
                  label="Call"
                  onClick={handleQuickCall}
                  variant="secondary"
                />
                <QuickActionButton
                  icon={<MailIcon className="w-4 h-4" />}
                  label="Email"
                  onClick={handleQuickEmail}
                  variant="secondary"
                />
                <QuickActionButton
                  icon={<CalendarIcon className="w-4 h-4" />}
                  label="Meeting"
                  onClick={handleScheduleMeeting}
                  variant="secondary"
                />
                <QuickActionButton
                  icon={<BellIcon className="w-4 h-4" />}
                  label="Remind"
                  onClick={() => alert('Setting smart reminder...')}
                  variant="secondary"
                  badge="3"
                />
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* AI Snapshot and Immediate Recommendations */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* AI Political Intelligence */}
        <Card>
          <div className="p-6">
            <div className="flex items-start gap-3 mb-4">
              <div className="bg-purple-100 p-2 rounded-lg">
                <SparklesIcon className="w-5 h-5 text-purple-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-bold text-gray-900 mb-2">AI Political Intelligence</h3>
                <p className="text-gray-700 text-sm leading-relaxed">{aiSnapshot}</p>
              </div>
            </div>
          </div>
        </Card>

        {/* Immediate Recommendations */}
        <Card>
          <div className="p-6">
            <div className="flex items-center gap-2 mb-4">
              <TrophyIcon className="w-5 h-5 text-gold-600" />
              <h3 className="font-bold text-gray-900">Immediate Recommendations</h3>
              <Badge className="bg-red-100 text-red-800 border-red-200 text-xs">Urgent</Badge>
            </div>
            <div className="space-y-3">
              {nextBestActions.slice(0, 2).map((action, index) => (
                <div key={index} className="p-3 border border-gray-200 rounded-lg">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 text-sm">{action.action}</h4>
                      <p className="text-xs text-gray-600 mt-1">{action.expectedOutcome} • {action.confidence}</p>
                    </div>
                    <Badge className={`${
                      action.priority === 'High' ? 'bg-red-100 text-red-800 border-red-200' :
                      'bg-yellow-100 text-yellow-800 border-yellow-200'
                    } text-xs`}>
                      {action.priority}
                    </Badge>
                  </div>
                  <Button variant="primary" size="xs" className="w-full">
                    Execute Now
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </Card>
      </div>

      {/* Compact Intelligence Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Contact & Giving Intelligence */}
        <Card>
          <div className="p-6">
            <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
              <PhoneIcon className="w-5 h-5 text-blue-600" />
              Contact & Giving
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Preferred Method:</span>
                <span className="font-medium">{contactIntelligence.preferredMethod}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Best Times:</span>
                <span className="font-medium">{contactIntelligence.bestTimes}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Lifetime Total:</span>
                <span className="font-medium">{givingIntelligence.totalLifetime}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Peak Months:</span>
                <span className="font-medium">{givingIntelligence.peakMonths}</span>
              </div>
              <div className="pt-2 border-t">
                <span className="text-gray-600 text-sm">Motivations:</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {givingIntelligence.motivations.slice(0, 2).map((motivation, index) => (
                    <Badge key={index} className="bg-blue-100 text-blue-800 border-blue-200 text-xs">
                      {motivation}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* Risk & Opportunities */}
        <Card>
          <div className="p-6">
            <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
              <TrendingUpIcon className="w-5 h-5 text-green-600" />
              Risk & Opportunities
            </h3>
            <div className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-600">Churn Risk:</span>
                  <Badge className="bg-green-100 text-green-800 border-green-200 text-xs">Low (15%)</Badge>
                </div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-600">Donation Likelihood:</span>
                  <span className="text-sm font-medium text-green-600">92% (30 days)</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Optimal Ask:</span>
                  <span className="text-sm font-medium">$750 - $1,200</span>
                </div>
              </div>
              <div className="pt-3 border-t">
                <div className="text-sm text-gray-600 mb-2">Top Opportunities:</div>
                <div className="space-y-1">
                  <div className="p-2 bg-purple-50 rounded border border-purple-200">
                    <div className="text-xs font-medium text-purple-900">Advisory Committee</div>
                    <div className="text-xs text-purple-700">$5K+ potential • 78% confidence</div>
                  </div>
                  <div className="p-2 bg-green-50 rounded border border-green-200">
                    <div className="text-xs font-medium text-green-900">Host Committee</div>
                    <div className="text-xs text-green-700">$10K+ potential • 82% confidence</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* Digital Engagement */}
        <Card>
          <div className="p-6">
            <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
              <ComputerDesktopIcon className="w-5 h-5 text-purple-600" />
              Digital Engagement
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Email Open Rate:</span>
                <span className="font-medium text-green-600">{digitalEngagement.emailOpen}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Website Visits:</span>
                <span className="font-medium">{digitalEngagement.websiteVisits}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Volunteer Hours:</span>
                <span className="font-medium text-red-600">{digitalEngagement.volunteerHours}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Event Attendance:</span>
                <span className="font-medium">{digitalEngagement.eventAttendance}</span>
              </div>
              <div className="pt-2 border-t">
                <div className="text-sm text-gray-600 mb-2">Political Engagement Score:</div>
                <div className="flex items-center gap-2">
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div className="bg-indigo-600 h-2 rounded-full w-11/12"></div>
                  </div>
                  <span className="text-sm font-bold text-indigo-600">94/100</span>
                </div>
                <div className="text-xs text-gray-500 mt-1">Highly Engaged Supporter</div>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Detailed Actions Section */}
      <Card>
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-bold text-gray-900 flex items-center gap-2">
              <TrophyIcon className="w-5 h-5 text-gold-600" />
              Complete Action Plan
            </h3>
            <Button
              variant="secondary"
              size="sm"
              onClick={() => setActiveSection(activeSection === 'actions' ? 'overview' : 'actions')}
            >
              {activeSection === 'actions' ? 'Show Less' : 'Show All Actions'}
            </Button>
          </div>

          {activeSection === 'actions' ? (
            <div className="space-y-4">
              {nextBestActions.map((action, index) => (
                <div key={index} className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900">{action.action}</h4>
                      <p className="text-sm text-gray-600 mt-1">{action.reason}</p>
                    </div>
                    <Badge className={`${
                      action.priority === 'High' ? 'bg-red-100 text-red-800 border-red-200' :
                      action.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                      'bg-green-100 text-green-800 border-green-200'
                    } text-xs`}>
                      {action.priority}
                    </Badge>
                  </div>
                  <div className="grid grid-cols-3 gap-4 text-sm mb-3">
                    <div>
                      <span className="text-gray-500">Expected:</span>
                      <div className="font-medium">{action.expectedOutcome}</div>
                    </div>
                    <div>
                      <span className="text-gray-500">Timeframe:</span>
                      <div className="font-medium">{action.timeframe}</div>
                    </div>
                    <div>
                      <span className="text-gray-500">Confidence:</span>
                      <div className="font-medium text-green-600">{action.confidence}</div>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="primary" size="sm">Execute Action</Button>
                    <Button variant="secondary" size="sm">Schedule</Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-4">
              <p className="text-gray-600 text-sm">Click "Show All Actions" to see the complete strategic action plan with detailed recommendations and execution options.</p>
            </div>
          )}
        </div>
      </Card>

    </div>
  );
};

export default EnhancedDonorProfile;
