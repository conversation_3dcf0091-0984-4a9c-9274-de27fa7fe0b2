import React, { useState } from 'react';
import { View } from '../../types';
import Card from '../ui/Card';
import Button from '../ui/Button';
import Badge from '../ui/Badge';
import DonorProfileModal from '../ui/DonorProfileModal';
import { getDonorProfileByName } from '../../utils/mockDonorProfiles';
import { Donor } from '../../types';
import {
  UsersIcon,
  SparklesIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  ExclamationTriangleIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  BriefcaseIcon,
  CheckCircleIcon,
  ClockIcon,
  EyeIcon,
  ArrowPathRoundedSquareIcon,
  LightBulbIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  DocumentTextIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  PuzzlePieceIcon,
  ArrowPathIcon
} from '../../constants';

interface PeopleDashboardProps {
  setView: (view: View) => void;
  setProfileId: (id: string) => void;
}

// Mock data for the dashboard
const peopleStats = {
  totalPeople: 245678,
  totalDonors: 199138,
  peopleChange: 2.3,
  donorsChange: 1.8,
  newThisMonth: 1247,
  lapsedThisMonth: 892
};

const dataCompleteness = {
  address: { complete: 89, missing: 11, count: 27045 },
  email: { complete: 76, missing: 24, count: 58912 },
  phone: { complete: 62, missing: 38, count: 93346 },
  employer: { complete: 45, missing: 55, count: 135123 },
  registration: { complete: 78, missing: 22, count: 54049 }
};

const topCategories = {
  flags: [
    { name: 'Major Donor', count: 1247, color: 'bg-green-100 text-green-800' },
    { name: 'Board Member', count: 89, color: 'bg-purple-100 text-purple-800' },
    { name: 'Volunteer', count: 3456, color: 'bg-blue-100 text-blue-800' },
    { name: 'VIP', count: 234, color: 'bg-yellow-100 text-yellow-800' },
    { name: 'Lapsed', count: 5678, color: 'bg-red-100 text-red-800' }
  ],
  cities: [
    { name: 'Miami', count: 12456, percentage: 12.5, raised: 2340000 },
    { name: 'Tampa', count: 9876, percentage: 9.9, raised: 1890000 },
    { name: 'Orlando', count: 8765, percentage: 8.8, raised: 1650000 },
    { name: 'Jacksonville', count: 7654, percentage: 7.7, raised: 1420000 },
    { name: 'Fort Lauderdale', count: 6543, percentage: 6.6, raised: 1280000 }
  ]
};

const aiSegments = [
  {
    id: 'comeback-crew',
    name: 'Comeback Crew',
    icon: ArrowPathIcon,
    count: 1571,
    potential: 113000,
    description: 'Lapsed donors with high re-engagement probability'
  },
  {
    id: 'level-up-list',
    name: 'Level-Up List',
    icon: LightBulbIcon,
    count: 578,
    potential: 21000,
    description: 'Mid-level donors ready for upgrade asks'
  },
  {
    id: 'new-faces',
    name: 'New Faces Welcome',
    icon: SparklesIcon,
    count: 185,
    potential: 6500,
    description: 'Recent donors needing welcome outreach'
  },
  {
    id: 'neighborhood-mvps',
    name: 'Neighborhood MVPs',
    icon: MapPinIcon,
    count: 303,
    potential: 104000,
    description: 'High-capacity prospects in key areas'
  },
  {
    id: 'quiet-giants',
    name: 'Quiet Giants',
    icon: EyeIcon,
    count: 7,
    potential: 6000,
    description: 'High-capacity, low-touch donors'
  }
];

const smartSuggestions = [
  {
    id: 1,
    type: 'donor',
    icon: CurrencyDollarIcon,
    message: 'Joseph Banks is ready for a $500 ask this month.',
    action: 'View Profile',
    priority: 'high'
  },
  {
    id: 2,
    type: 'segment',
    icon: EnvelopeIcon,
    message: '185 new donors need welcome outreach.',
    action: 'View List',
    priority: 'medium'
  },
  {
    id: 3,
    type: 'cultivation',
    icon: PhoneIcon,
    message: '7 Quiet Giants ready for personal cultivation.',
    action: 'Call List',
    priority: 'high'
  },
  {
    id: 4,
    type: 'data',
    icon: ExclamationTriangleIcon,
    message: '2,150 donors missing employer data.',
    action: 'Fix Now',
    priority: 'low'
  }
];

const PeopleDashboard: React.FC<PeopleDashboardProps> = ({ setView, setProfileId }) => {
  const [selectedDonor, setSelectedDonor] = useState<Donor | null>(null);
  const [showDonorProfile, setShowDonorProfile] = useState(false);

  const handleViewProfile = () => {
    setProfileId('joseph-banks');
    setView('profile');
  };

  const handleDonorClick = (donorName: string) => {
    const donor = getDonorProfileByName(donorName);
    if (donor) {
      setSelectedDonor(donor);
      setShowDonorProfile(true);
    }
  };

  const StatCard: React.FC<{ 
    title: string; 
    value: string; 
    change?: number; 
    icon: React.ComponentType<any>;
    subtitle?: string;
  }> = ({ title, value, change, icon: Icon, subtitle }) => (
    <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {subtitle && <p className="text-xs text-gray-500 mt-1">{subtitle}</p>}
        </div>
        <div className="flex flex-col items-end">
          <Icon className="w-8 h-8 text-crimson-blue mb-2" />
          {change !== undefined && (
            <div className={`flex items-center text-sm ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {change >= 0 ? (
                <ArrowTrendingUpIcon className="w-4 h-4 mr-1" />
              ) : (
                <ArrowTrendingDownIcon className="w-4 h-4 mr-1" />
              )}
              {Math.abs(change)}%
            </div>
          )}
        </div>
      </div>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">People Dashboard</h1>
        <p className="text-gray-600 mt-1">Your AI-powered command center for donor management and insights.</p>
      </div>

      {/* Core Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title="Total People"
          value={peopleStats.totalPeople.toLocaleString()}
          change={peopleStats.peopleChange}
          icon={UsersIcon}
          subtitle={`+${peopleStats.newThisMonth.toLocaleString()} this month`}
        />
        <StatCard
          title="All-Time Donors"
          value={peopleStats.totalDonors.toLocaleString()}
          change={peopleStats.donorsChange}
          icon={CurrencyDollarIcon}
          subtitle="Active & lapsed combined"
        />
        <StatCard
          title="New This Month"
          value={peopleStats.newThisMonth.toLocaleString()}
          icon={SparklesIcon}
          subtitle="First-time donors"
        />
        <StatCard
          title="Lapsed This Month"
          value={peopleStats.lapsedThisMonth.toLocaleString()}
          icon={ClockIcon}
          subtitle="Need re-engagement"
        />
      </div>

      {/* AI Smart Segments Preview */}
      <Card>
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                <SparklesIcon className="w-6 h-6 text-crimson-blue" />
                AI Smart Segments
              </h2>
              <p className="text-gray-600">Live segments powered by AI to maximize your fundraising impact.</p>
            </div>
            <Button variant="secondary" size="sm">
              <PlusIcon className="w-4 h-4 mr-2" />
              Create Custom
            </Button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
            {aiSegments.map((segment) => (
              <div
                key={segment.id}
                className="bg-gradient-to-br from-white to-gray-50 border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all cursor-pointer group"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="w-10 h-10 bg-crimson-blue/10 rounded-lg flex items-center justify-center">
                    <segment.icon className="w-5 h-5 text-crimson-blue" />
                  </div>
                  <Badge color="blue" className="text-xs">
                    ${(segment.potential / 1000).toFixed(0)}K
                  </Badge>
                </div>
                <h3 className="font-semibold text-gray-900 mb-1 group-hover:text-crimson-blue transition-colors">
                  {segment.name}
                </h3>
                <p className="text-sm text-gray-600 mb-3">{segment.description}</p>
                <div className="flex items-center justify-between">
                  <span className="text-lg font-bold text-crimson-blue">
                    {segment.count.toLocaleString()}
                  </span>
                  <div className="flex gap-1">
                    <Button variant="ghost" size="xs">
                      <EyeIcon className="w-3 h-3" />
                    </Button>
                    <Button variant="ghost" size="xs">
                      <ArrowPathRoundedSquareIcon className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Card>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Data Health & Categories */}
        <div className="lg:col-span-2 space-y-6">
          {/* Data Health Snapshot */}
          <Card>
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                    <ChartBarIcon className="w-6 h-6 text-crimson-blue" />
                    Data Health Snapshot
                  </h2>
                  <p className="text-gray-600">Track data completeness and potential revenue at risk.</p>
                </div>
                <Button variant="primary" size="sm">
                  <DocumentTextIcon className="w-4 h-4 mr-2" />
                  Fill the Gaps
                </Button>
              </div>

              <div className="space-y-4">
                {Object.entries(dataCompleteness).map(([field, data]) => (
                  <div key={field} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700 capitalize">
                        {field === 'registration' ? 'Voter Registration' : field}
                      </span>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-600">
                          {data.missing}% missing ({data.count.toLocaleString()})
                        </span>
                        <div className={`w-3 h-3 rounded-full ${
                          data.complete >= 80 ? 'bg-green-500' :
                          data.complete >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                        }`} />
                      </div>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          data.complete >= 80 ? 'bg-green-500' :
                          data.complete >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${data.complete}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>

          {/* Fill the Gaps Panel */}
          <Card>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-6">
                <PuzzlePieceIcon className="w-6 h-6 text-crimson-blue" />
                <h2 className="text-xl font-bold text-gray-900">Fill the Gaps</h2>
              </div>

              <div className="space-y-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <BriefcaseIcon className="w-5 h-5 text-blue-600" />
                      <span className="font-medium text-blue-900">Missing Employer/Occupation</span>
                    </div>
                    <Badge color="blue">135K missing</Badge>
                  </div>
                  <p className="text-sm text-blue-800 mb-3">2,150 donors missing employer/occupation. Predict now?</p>
                  <Button variant="primary" size="sm">
                    <SparklesIcon className="w-4 h-4 mr-2" />
                    Predict & Fill
                  </Button>
                </div>

                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <PhoneIcon className="w-5 h-5 text-green-600" />
                      <span className="font-medium text-green-900">Missing Phone Numbers</span>
                    </div>
                    <Badge color="green">93K missing</Badge>
                  </div>
                  <p className="text-sm text-green-800 mb-3">3,890 missing phone numbers. Append from voter file?</p>
                  <Button variant="secondary" size="sm">
                    <CheckCircleIcon className="w-4 h-4 mr-2" />
                    Append Now
                  </Button>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <EnvelopeIcon className="w-5 h-5 text-yellow-600" />
                      <span className="font-medium text-yellow-900">Missing Email Addresses</span>
                    </div>
                    <Badge color="yellow">59K missing</Badge>
                  </div>
                  <p className="text-sm text-yellow-800 mb-3">Email addresses missing for 24% of donors.</p>
                  <Button variant="secondary" size="sm">
                    <MagnifyingGlassIcon className="w-4 h-4 mr-2" />
                    Find Emails
                  </Button>
                </div>
              </div>
            </div>
          </Card>

          {/* Top Categories */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Top Flags */}
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Top 5 Flags</h3>
                <div className="space-y-3">
                  {topCategories.flags.map((flag, index) => (
                    <div key={flag.name} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <span className="text-sm font-medium text-gray-500">#{index + 1}</span>
                        <Badge className={flag.color}>{flag.name}</Badge>
                      </div>
                      <span className="text-sm font-semibold text-gray-900">
                        {flag.count.toLocaleString()}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </Card>

            {/* Top Cities */}
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Top 5 Cities</h3>
                <div className="space-y-3">
                  {topCategories.cities.map((city, index) => (
                    <div key={city.name} className="space-y-1">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <span className="text-sm font-medium text-gray-500">#{index + 1}</span>
                          <span className="font-medium text-gray-900">{city.name}</span>
                        </div>
                        <span className="text-sm font-semibold text-crimson-blue">
                          {city.count.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-xs text-gray-600 ml-8">
                        <span>{city.percentage}% of donors</span>
                        <span>${(city.raised / 1000000).toFixed(1)}M raised</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </Card>
          </div>
        </div>

        {/* Right Column - Smart Suggestions & Quick Wins */}
        <div className="space-y-6">
          <Card>
            <div className="p-6">
              <h2 className="text-xl font-bold text-gray-900 flex items-center gap-2 mb-6">
                <LightBulbIcon className="w-6 h-6 text-crimson-blue" />
                Smart Suggestions
              </h2>

              <div className="space-y-4">
                {smartSuggestions.map((suggestion) => (
                  <div
                    key={suggestion.id}
                    className={`p-4 rounded-lg border-l-4 ${
                      suggestion.priority === 'high' ? 'border-red-500 bg-red-50' :
                      suggestion.priority === 'medium' ? 'border-yellow-500 bg-yellow-50' :
                      'border-blue-500 bg-blue-50'
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      <suggestion.icon className={`w-5 h-5 mt-0.5 ${
                        suggestion.priority === 'high' ? 'text-red-600' :
                        suggestion.priority === 'medium' ? 'text-yellow-600' :
                        'text-blue-600'
                      }`} />
                      <div className="flex-1">
                        <p className="text-sm text-gray-800 mb-2">{suggestion.message}</p>
                        <Button
                          variant="ghost"
                          size="xs"
                          onClick={suggestion.type === 'donor' ? handleViewProfile : undefined}
                          className={`${
                            suggestion.priority === 'high' ? 'text-red-700 hover:bg-red-100' :
                            suggestion.priority === 'medium' ? 'text-yellow-700 hover:bg-yellow-100' :
                            'text-blue-700 hover:bg-blue-100'
                          }`}
                        >
                          {suggestion.action}
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>

          {/* Quick Wins Sidebar */}
          <Card>
            <div className="p-6">
              <h2 className="text-xl font-bold text-gray-900 flex items-center gap-2 mb-6">
                <SparklesIcon className="w-6 h-6 text-crimson-blue" />
                Quick Wins
              </h2>

              <div className="space-y-4">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <LightBulbIcon className="w-5 h-5 text-blue-600" />
                    <span className="font-medium text-blue-900">Try New Smart Segment</span>
                  </div>
                  <p className="text-sm text-blue-800 mb-3">FL + TX High-Dollar prospects ready for outreach.</p>
                  <Button variant="secondary" size="sm" className="w-full">
                    Create Segment
                  </Button>
                </div>

                <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <EyeIcon className="w-5 h-5 text-green-600" />
                    <span className="font-medium text-green-900">Donor Snapshot</span>
                  </div>
                  <p className="text-sm text-green-800 mb-3">Click any profile to view AI-powered insights.</p>
                  <Button
                    variant="secondary"
                    size="sm"
                    className="w-full"
                    onClick={handleViewProfile}
                  >
                    View Example
                  </Button>
                </div>

                <div className="bg-gradient-to-r from-purple-50 to-violet-50 border border-purple-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <DocumentTextIcon className="w-5 h-5 text-purple-600" />
                    <span className="font-medium text-purple-900">Clean Your Data</span>
                  </div>
                  <p className="text-sm text-purple-800 mb-3">Test 'Fill the Gaps' tool for missing data.</p>
                  <Button variant="secondary" size="sm" className="w-full">
                    Start Cleaning
                  </Button>
                </div>

                <div className="bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <CurrencyDollarIcon className="w-5 h-5 text-orange-600" />
                    <span className="font-medium text-orange-900">Boost Next Ask</span>
                  </div>
                  <p className="text-sm text-orange-800 mb-3">Ready to increase your success rate?</p>
                  <Button variant="primary" size="sm" className="w-full">
                    Get Started
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Donor Profile Modal */}
      <DonorProfileModal
        donor={selectedDonor}
        isOpen={showDonorProfile}
        onClose={() => setShowDonorProfile(false)}
      />
    </div>
  );
};

export default PeopleDashboard;
