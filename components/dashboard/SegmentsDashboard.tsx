import React from 'react';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { SparklesIcon } from '../../constants';

interface Segment {
  id: string;
  funName: string;
  originalName: string;
  description: string;
  count: number;
  value: string;
  emoji: string;
}

const segments: Segment[] = [
  {
    id: 'comeback-crew',
    funName: 'Comeback Crew',
    originalName: 'Lapsed Donors',
    description: 'Ideal for reactivation campaigns and calling programs.',
    count: 1653,
    value: 'Reactivation',
    emoji: '🔄'
  },
  {
    id: 'level-up-list',
    funName: 'Level-Up List',
    originalName: 'Mid-Level Upgrade Candidates ($500 avg gift)',
    description: 'Perfect for upgrade asks and personal calls.',
    count: 578,
    value: 'Upgrade',
    emoji: '💡'
  },
  {
    id: 'frequent-flyers',
    funName: 'Frequent Flyers',
    originalName: 'Small Dollar, High Frequency (5+ gifts)',
    description: 'Strong candidates for monthly giving invites.',
    count: 491,
    value: 'Monthly',
    emoji: '🔂'
  },
  {
    id: 'new-faces',
    funName: 'New Faces Welcome',
    originalName: 'New Donors (Last 90 Days)',
    description: 'Stewardship and welcome call opportunities.',
    count: 929,
    value: 'Stewardship',
    emoji: '✨'
  },
  {
    id: 'neighborhood-mvps',
    funName: 'Neighborhood MVPs',
    originalName: 'Potential Major Donors by ZIP ($500+ avg, low # gifts)',
    description: 'Personal cultivation for major giving.',
    count: 13,
    value: 'Major Gifts',
    emoji: '🗺️'
  },
  {
    id: 'next-gift-predictors',
    funName: 'Next Gift Predictors',
    originalName: 'Recurrence Prediction Cohort (likely to give again soon)',
    description: 'Queue for renewal reminders or stewardship touchpoints.',
    count: 640,
    value: 'Renewal',
    emoji: '🔮'
  }
];

const SegmentsDashboard: React.FC = () => {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-text-primary">AI Curated Segments</h2>
        <p className="text-text-secondary">Smart donor segments powered by AI to maximize your fundraising impact.</p>
      </div>
      
      <Card>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-base-300">
                <th className="text-left py-3 px-4 font-semibold text-text-primary">Segment</th>
                <th className="text-left py-3 px-4 font-semibold text-text-primary">Original Name</th>
                <th className="text-center py-3 px-4 font-semibold text-text-primary">Count</th>
                <th className="text-left py-3 px-4 font-semibold text-text-primary">Why It's Valuable</th>
                <th className="text-center py-3 px-4 font-semibold text-text-primary">Actions</th>
              </tr>
            </thead>
            <tbody>
              {segments.map((segment) => (
                <tr key={segment.id} className="border-b border-base-200 hover:bg-base-50">
                  <td className="py-4 px-4">
                    <div className="flex items-center gap-3">
                      <span className="text-lg">{segment.emoji}</span>
                      <div>
                        <h4 className="font-semibold text-text-primary">{segment.funName}</h4>
                        <p className="text-xs text-text-secondary">{segment.value}</p>
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <p className="text-sm text-text-secondary">{segment.originalName}</p>
                  </td>
                  <td className="py-4 px-4 text-center">
                    <span className="font-bold text-lg text-text-primary">{segment.count.toLocaleString()}</span>
                  </td>
                  <td className="py-4 px-4">
                    <p className="text-sm text-text-secondary">{segment.description}</p>
                  </td>
                  <td className="py-4 px-4">
                    <div className="flex gap-2 justify-center">
                      <Button variant="secondary" size="sm">View List</Button>
                      <Button variant="secondary" size="sm">Create Smart List</Button>
                      <Button size="sm">Launch Campaign</Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card title="Segment Performance" className="lg:col-span-2">
          <div className="space-y-4">
            <div className="flex justify-between items-center p-3 bg-green-50 border border-green-200 rounded-lg">
              <div>
                <h5 className="font-semibold text-green-800">Top Performing Segment</h5>
                <p className="text-sm text-green-600">Comeback Crew - 23% response rate</p>
              </div>
              <SparklesIcon className="w-6 h-6 text-green-600" />
            </div>
            <div className="flex justify-between items-center p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div>
                <h5 className="font-semibold text-blue-800">Most Valuable Segment</h5>
                <p className="text-sm text-blue-600">Neighborhood MVPs - $2,400 avg gift</p>
              </div>
              <SparklesIcon className="w-6 h-6 text-blue-600" />
            </div>
            <div className="flex justify-between items-center p-3 bg-purple-50 border border-purple-200 rounded-lg">
              <div>
                <h5 className="font-semibold text-purple-800">Fastest Growing</h5>
                <p className="text-sm text-purple-600">New Faces Welcome - +15% this month</p>
              </div>
              <SparklesIcon className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </Card>

        <div className="space-y-6">
          <Card title="Quick Actions">
            <div className="space-y-3">
              <Button className="w-full">Create New Segment</Button>
              <Button variant="secondary" className="w-full">Import Segment</Button>
              <Button variant="secondary" className="w-full">Export All Segments</Button>
            </div>
          </Card>
          
          <Card title="AI Recommendations">
            <div className="space-y-3 text-sm">
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-yellow-800"><strong>Tip:</strong> Combine "Level-Up List" with "Frequent Flyers" for a powerful upgrade campaign.</p>
              </div>
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-blue-800"><strong>Insight:</strong> "Comeback Crew" shows 40% higher engagement on Tuesdays.</p>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default SegmentsDashboard;
