import React, { useState } from 'react';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { ChartBarIcon, ClockIcon, UserGroupIcon, CurrencyDollarIcon } from '../../constants';

interface AnalyticsData {
  segment: string;
  emoji: string;
  weeklyData: {
    week: string;
    contacts: number;
    responses: number;
    conversions: number;
    revenue: number;
    conversionRate: number;
    avgResponseTime: number;
  }[];
  bestPerformingDay: string;
  bestPerformingTime: string;
  topChannel: string;
  avgGiftTrend: 'up' | 'down' | 'stable';
}

const PerformanceAnalytics: React.FC = () => {
  const [selectedMetric, setSelectedMetric] = useState<'conversion' | 'revenue' | 'response' | 'efficiency'>('conversion');
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');

  const analyticsData: AnalyticsData[] = [
    {
      segment: 'Neighborhood MVPs',
      emoji: '🏆',
      weeklyData: [
        { week: 'Week 1', contacts: 25, responses: 18, conversions: 6, revenue: 7200, conversionRate: 24, avgResponseTime: 2.5 },
        { week: 'Week 2', contacts: 30, responses: 22, conversions: 8, revenue: 9600, conversionRate: 27, avgResponseTime: 2.1 },
        { week: 'Week 3', contacts: 28, responses: 20, conversions: 7, revenue: 8400, conversionRate: 25, avgResponseTime: 2.3 },
        { week: 'Week 4', contacts: 32, responses: 25, conversions: 9, revenue: 10800, conversionRate: 28, avgResponseTime: 1.9 }
      ],
      bestPerformingDay: 'Tuesday',
      bestPerformingTime: '10:00 AM',
      topChannel: 'Phone Call',
      avgGiftTrend: 'up'
    },
    {
      segment: 'Comeback Crew',
      emoji: '🔄',
      weeklyData: [
        { week: 'Week 1', contacts: 150, responses: 45, conversions: 12, revenue: 2100, conversionRate: 8, avgResponseTime: 4.2 },
        { week: 'Week 2', contacts: 180, responses: 54, conversions: 15, revenue: 2700, conversionRate: 8.3, avgResponseTime: 3.8 },
        { week: 'Week 3', contacts: 165, responses: 52, conversions: 14, revenue: 2520, conversionRate: 8.5, avgResponseTime: 3.5 },
        { week: 'Week 4', contacts: 200, responses: 68, conversions: 18, revenue: 3240, conversionRate: 9, avgResponseTime: 3.2 }
      ],
      bestPerformingDay: 'Tuesday',
      bestPerformingTime: '2:00 PM',
      topChannel: 'Email',
      avgGiftTrend: 'up'
    },
    {
      segment: 'Quiet Giants',
      emoji: '🤫',
      weeklyData: [
        { week: 'Week 1', contacts: 5, responses: 4, conversions: 2, revenue: 5000, conversionRate: 40, avgResponseTime: 1.5 },
        { week: 'Week 2', contacts: 4, responses: 3, conversions: 2, revenue: 6000, conversionRate: 50, avgResponseTime: 1.2 },
        { week: 'Week 3', contacts: 3, responses: 3, conversions: 1, revenue: 2500, conversionRate: 33, avgResponseTime: 1.8 },
        { week: 'Week 4', contacts: 6, responses: 5, conversions: 3, revenue: 9000, conversionRate: 50, avgResponseTime: 1.1 }
      ],
      bestPerformingDay: 'Wednesday',
      bestPerformingTime: '11:00 AM',
      topChannel: 'Personal Meeting',
      avgGiftTrend: 'up'
    }
  ];

  const getMetricData = (data: AnalyticsData[], metric: string) => {
    return data.map(segment => ({
      name: segment.segment,
      emoji: segment.emoji,
      data: segment.weeklyData.map(week => {
        switch (metric) {
          case 'conversion': return week.conversionRate;
          case 'revenue': return week.revenue;
          case 'response': return (week.responses / week.contacts) * 100;
          case 'efficiency': return week.revenue / week.contacts;
          default: return week.conversionRate;
        }
      })
    }));
  };

  const formatMetricValue = (value: number, metric: string) => {
    switch (metric) {
      case 'conversion':
      case 'response':
        return `${value.toFixed(1)}%`;
      case 'revenue':
        return `$${value.toLocaleString()}`;
      case 'efficiency':
        return `$${value.toFixed(0)}`;
      default:
        return value.toString();
    }
  };

  const getMetricLabel = (metric: string) => {
    switch (metric) {
      case 'conversion': return 'Conversion Rate';
      case 'revenue': return 'Revenue';
      case 'response': return 'Response Rate';
      case 'efficiency': return 'Revenue per Contact';
      default: return 'Conversion Rate';
    }
  };

  const metricData = getMetricData(analyticsData, selectedMetric);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-text-primary flex items-center gap-2">
            <ChartBarIcon className="w-6 h-6 text-purple-600" />
            Performance Analytics
          </h2>
          <p className="text-text-secondary">Deep dive into conversion rates and performance trends</p>
        </div>
        <div className="flex gap-2">
          {(['7d', '30d', '90d', '1y'] as const).map((range) => (
            <Button
              key={range}
              variant={timeRange === range ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setTimeRange(range)}
            >
              {range}
            </Button>
          ))}
        </div>
      </div>

      {/* Metric Selector */}
      <Card>
        <div className="flex flex-wrap gap-2 mb-4">
          {(['conversion', 'revenue', 'response', 'efficiency'] as const).map((metric) => (
            <Button
              key={metric}
              variant={selectedMetric === metric ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setSelectedMetric(metric)}
            >
              {getMetricLabel(metric)}
            </Button>
          ))}
        </div>

        {/* Performance Chart Simulation */}
        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="font-semibold mb-4">{getMetricLabel(selectedMetric)} Trends</h3>
          <div className="space-y-4">
            {metricData.map((segment, index) => (
              <div key={index} className="border-b border-gray-200 pb-4 last:border-b-0">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium flex items-center gap-2">
                    <span className="text-lg">{segment.emoji}</span>
                    {segment.name}
                  </span>
                  <span className="text-sm text-gray-600">
                    Latest: {formatMetricValue(segment.data[segment.data.length - 1], selectedMetric)}
                  </span>
                </div>
                <div className="flex items-end gap-1 h-12">
                  {segment.data.map((value, idx) => (
                    <div
                      key={idx}
                      className="bg-blue-500 rounded-t flex-1 min-w-0"
                      style={{
                        height: `${(value / Math.max(...segment.data)) * 100}%`,
                        backgroundColor: `hsl(${200 + index * 60}, 70%, ${50 + (value / Math.max(...segment.data)) * 30}%)`
                      }}
                      title={`Week ${idx + 1}: ${formatMetricValue(value, selectedMetric)}`}
                    ></div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </Card>

      {/* Performance Insights Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-green-50 border-green-200">
          <div className="flex items-center gap-3">
            <UserGroupIcon className="w-8 h-8 text-green-600" />
            <div>
              <p className="text-sm text-green-600 font-medium">Best Conversion</p>
              <p className="text-xl font-bold text-green-800">50%</p>
              <p className="text-xs text-green-600">Quiet Giants</p>
            </div>
          </div>
        </Card>

        <Card className="bg-blue-50 border-blue-200">
          <div className="flex items-center gap-3">
            <ClockIcon className="w-8 h-8 text-blue-600" />
            <div>
              <p className="text-sm text-blue-600 font-medium">Avg Response Time</p>
              <p className="text-xl font-bold text-blue-800">2.1 hrs</p>
              <p className="text-xs text-blue-600">Across all segments</p>
            </div>
          </div>
        </Card>

        <Card className="bg-purple-50 border-purple-200">
          <div className="flex items-center gap-3">
            <ChartBarIcon className="w-8 h-8 text-purple-600" />
            <div>
              <p className="text-sm text-purple-600 font-medium">Growth Rate</p>
              <p className="text-xl font-bold text-purple-800">+23%</p>
              <p className="text-xs text-purple-600">Month over month</p>
            </div>
          </div>
        </Card>

        <Card className="bg-orange-50 border-orange-200">
          <div className="flex items-center gap-3">
            <CurrencyDollarIcon className="w-8 h-8 text-orange-600" />
            <div>
              <p className="text-sm text-orange-600 font-medium">Revenue/Contact</p>
              <p className="text-xl font-bold text-orange-800">$337</p>
              <p className="text-xs text-orange-600">Average efficiency</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Detailed Performance Table */}
      <Card title="📊 Detailed Performance Breakdown">
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-semibold">Segment</th>
                <th className="text-center py-3 px-4 font-semibold">Best Day</th>
                <th className="text-center py-3 px-4 font-semibold">Best Time</th>
                <th className="text-center py-3 px-4 font-semibold">Top Channel</th>
                <th className="text-center py-3 px-4 font-semibold">Avg Response</th>
                <th className="text-center py-3 px-4 font-semibold">Trend</th>
                <th className="text-center py-3 px-4 font-semibold">Actions</th>
              </tr>
            </thead>
            <tbody>
              {analyticsData.map((segment, index) => (
                <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-3 px-4">
                    <div className="flex items-center gap-2">
                      <span className="text-lg">{segment.emoji}</span>
                      <span className="font-medium">{segment.segment}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-center">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                      {segment.bestPerformingDay}
                    </span>
                  </td>
                  <td className="py-3 px-4 text-center">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
                      {segment.bestPerformingTime}
                    </span>
                  </td>
                  <td className="py-3 px-4 text-center">
                    <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs">
                      {segment.topChannel}
                    </span>
                  </td>
                  <td className="py-3 px-4 text-center">
                    <span className="font-medium">
                      {segment.weeklyData[segment.weeklyData.length - 1].avgResponseTime}h
                    </span>
                  </td>
                  <td className="py-3 px-4 text-center">
                    <span className={`px-2 py-1 rounded text-xs ${
                      segment.avgGiftTrend === 'up' ? 'bg-green-100 text-green-800' :
                      segment.avgGiftTrend === 'down' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {segment.avgGiftTrend === 'up' ? '📈 Up' : 
                       segment.avgGiftTrend === 'down' ? '📉 Down' : '➡️ Stable'}
                    </span>
                  </td>
                  <td className="py-3 px-4 text-center">
                    <div className="flex gap-1 justify-center">
                      <Button variant="outline" size="sm">📊 Details</Button>
                      <Button size="sm">⚡ Optimize</Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      {/* Actionable Insights */}
      <Card title="🎯 Actionable Insights">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <h4 className="font-semibold text-green-600">🚀 Optimization Opportunities</h4>
            <div className="space-y-2 text-sm">
              <div className="p-2 bg-green-50 rounded border border-green-200">
                <strong>Tuesday 10 AM:</strong> Schedule more Neighborhood MVP calls - 40% higher conversion
              </div>
              <div className="p-2 bg-blue-50 rounded border border-blue-200">
                <strong>Email timing:</strong> Comeback Crew responds best at 2 PM on weekdays
              </div>
              <div className="p-2 bg-purple-50 rounded border border-purple-200">
                <strong>Personal touch:</strong> Quiet Giants prefer in-person meetings (50% conversion)
              </div>
            </div>
          </div>
          
          <div className="space-y-3">
            <h4 className="font-semibold text-orange-600">⚠️ Areas for Improvement</h4>
            <div className="space-y-2 text-sm">
              <div className="p-2 bg-orange-50 rounded border border-orange-200">
                <strong>Response time:</strong> Comeback Crew taking 3.2h avg - aim for under 2h
              </div>
              <div className="p-2 bg-yellow-50 rounded border border-yellow-200">
                <strong>Weekend outreach:</strong> All segments show 60% lower response on weekends
              </div>
              <div className="p-2 bg-red-50 rounded border border-red-200">
                <strong>Follow-up gaps:</strong> 23% of initial contacts lack proper follow-up sequence
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default PerformanceAnalytics;
