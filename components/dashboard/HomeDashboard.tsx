
import React from 'react';
import { ReactNode } from 'react';
import { View } from '../../types';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { SparklesIcon, LightBulbIcon, CheckCircleIcon, ArrowTrendingUpIcon, UsersIcon, PuzzlePieceIcon } from '../../constants';

interface HomeDashboardProps {
  setView: (view: View) => void;
  setProfileId: (id: string) => void;
}

const ActionTile: React.FC<{ title: string; subtitle: ReactNode; cta: string; icon: ReactNode; onClick?: () => void; }> = ({ title, subtitle, cta, icon, onClick }) => (
  <Card className="flex flex-col" onClick={onClick}>
    <div className="flex-grow">
      <div className="flex items-start gap-4">
        <div className="text-crimson-blue bg-crimson-blue/10 p-3 rounded-lg">
          {icon}
        </div>
        <div>
          <h4 className="font-bold text-base text-text-primary">{title}</h4>
          <p className="text-sm text-text-secondary mt-1">{subtitle}</p>
        </div>
      </div>
    </div>
    <div className="mt-4">
      <Button variant="secondary" size="sm" className="w-full">{cta}</Button>
    </div>
  </Card>
);

const QuickStat: React.FC<{ label: string; value: string; }> = ({ label, value }) => (
    <div className="bg-base-100 p-4 rounded-lg shadow-sm">
        <p className="text-sm text-text-secondary">{label}</p>
        <p className="text-2xl font-bold text-text-primary mt-1">{value}</p>
    </div>
);

const HomeDashboard: React.FC<HomeDashboardProps> = ({ setView, setProfileId }) => {
  const handleViewProfile = () => {
    setProfileId('joseph-banks');
    setView('profile');
  };

  const handleCleanData = () => {
    setView('compliance');
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div className="lg:col-span-2 space-y-6">
        <div className="mb-4">
            <h2 className="text-2xl font-bold text-text-primary">Welcome back, Sofia!</h2>
            <p className="text-text-secondary">Here’s what you can take action on today.</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <ActionTile
            icon={<SparklesIcon className="w-6 h-6" />}
            title="Predictive Ask Readiness"
            subtitle={<><strong>Joseph Banks</strong> is ready for a $500 ask this month.</>}
            cta="View Profile"
            onClick={handleViewProfile}
          />
          <ActionTile
            icon={<ArrowTrendingUpIcon className="w-6 h-6" />}
            title="Recurrence Prediction"
            subtitle={<><strong>12 donors</strong> are due to give again this July.</>}
            cta="View List"
          />
          <ActionTile
            icon={<UsersIcon className="w-6 h-6" />}
            title="Tier Movement Alert"
            subtitle={<><strong>78 donors</strong> just upgraded to Silver Tier.</>}
            cta="View Donors"
          />
          <ActionTile
            icon={<LightBulbIcon className="w-6 h-6" />}
            title="AI Smart Segments"
            subtitle={<>‘High-Dollar Republican Women 45-64’ is ready.</>}
            cta="Review Segment"
          />
          <ActionTile
            icon={<PuzzlePieceIcon className="w-6 h-6" />}
            title="Fill the Gaps"
            subtitle={<><strong>2,942 donors</strong> missing party data. Predict party now?</>}
            cta="Clean Data"
            onClick={handleCleanData}
          />
          <Card className="flex flex-col">
             <h4 className="font-bold text-base text-text-primary mb-3">Insights Feed</h4>
             <div className="space-y-3 text-sm text-text-secondary overflow-y-auto h-32 pr-2">
                <p><strong>Jane Smith</strong> is likely ready for a $250 ask.</p>
                <p><strong>7 TX donors</strong> are due for thank you calls this week.</p>
                <p>New segment 'Swing Donors' has <strong>43 new members</strong>.</p>
                <p><strong>Robert Davis's</strong> giving likelihood increased by 15%.</p>
             </div>
          </Card>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <QuickStat label="Total Donors" value="199,138" />
            <QuickStat label="Pledges Outstanding" value="$220,510" />
            <QuickStat label="Average Gift" value="$87.50" />
            <QuickStat label="% Active Voters" value="78%" />
            <QuickStat label="% Missing Contact" value="12%" />
        </div>
      </div>

      <div className="lg:col-span-1 space-y-6 lg:mt-14">
        <Card title="Quick Wins">
          <ul className="space-y-4">
            <li className="flex items-start gap-3">
              <CheckCircleIcon className="w-5 h-5 text-green-500 mt-0.5" />
              <p className="text-sm text-text-secondary">Try new <strong>“FL + TX High-Dollar”</strong> Smart Segment.</p>
            </li>
            <li className="flex items-start gap-3">
              <CheckCircleIcon className="w-5 h-5 text-green-500 mt-0.5" />
              <p className="text-sm text-text-secondary">Click a profile to view the new AI-powered <strong>Donor Snapshot</strong>.</p>
            </li>
            <li className="flex items-start gap-3">
              <CheckCircleIcon className="w-5 h-5 text-green-500 mt-0.5" />
              <p className="text-sm text-text-secondary">Test the <strong>“Fill the Gaps”</strong> tool with missing income data on the Compliance dashboard.</p>
            </li>
             <li className="flex items-start gap-3">
              <CheckCircleIcon className="w-5 h-5 text-green-500 mt-0.5" />
              <p className="text-sm text-text-secondary">Review <strong>12 donors</strong> with a high likelihood to recur their gift this month.</p>
            </li>
          </ul>
        </Card>
        <Card title="AI Curated Segments (Live Data)">
          <div className="space-y-3">
            <div className="p-3 bg-base-200 rounded-lg">
                <h5 className="font-semibold text-sm">🔄 Comeback Crew</h5>
                <p className="text-xs text-text-secondary">1,571 donors • ~$113,000 potential revenue</p>
                <p className="text-xs text-text-secondary mt-1">Suggested: Launch call/text reactivation campaign</p>
                <div className="flex gap-2 mt-2">
                  <Button variant="secondary" size="sm" className="flex-1">View List</Button>
                  <Button variant="secondary" size="sm" className="flex-1">Launch Campaign</Button>
                </div>
            </div>
            <div className="p-3 bg-base-200 rounded-lg">
                <h5 className="font-semibold text-sm">🗺️ Neighborhood MVPs</h5>
                <p className="text-xs text-text-secondary">303 donors • ~$104,000 potential revenue</p>
                <p className="text-xs text-text-secondary mt-1">Suggested: Schedule major donor calls/events</p>
                <div className="flex gap-2 mt-2">
                  <Button variant="secondary" size="sm" className="flex-1">View List</Button>
                  <Button variant="secondary" size="sm" className="flex-1">Launch Campaign</Button>
                </div>
            </div>
            <div className="p-3 bg-base-200 rounded-lg">
                <h5 className="font-semibold text-sm">💡 Level-Up List</h5>
                <p className="text-xs text-text-secondary">578 donors • ~$21,300 potential revenue</p>
                <p className="text-xs text-text-secondary mt-1">Suggested: Send upgrade ask emails & calls</p>
                <div className="flex gap-2 mt-2">
                  <Button variant="secondary" size="sm" className="flex-1">View List</Button>
                  <Button variant="secondary" size="sm" className="flex-1">Launch Campaign</Button>
                </div>
            </div>
            <Button variant="secondary" className="w-full mt-3" onClick={() => setView('fundraising')}>
              View All 8 Segments →
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default HomeDashboard;