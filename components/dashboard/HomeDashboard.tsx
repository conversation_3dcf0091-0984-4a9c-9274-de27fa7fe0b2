import React from 'react';
import { ReactNode } from 'react';
import { View } from '../../types';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { SparklesIcon, LightBulbIcon, CheckCircleIcon, ArrowTrendingUpIcon, UsersIcon, PuzzlePieceIcon, ArrowPathIcon, MapPinIcon, TrendingUpIcon } from '../../constants';
import RealTimeDonationTracker from './RealTimeDonationTracker';
import AIDailyBriefing from './AIDailyBriefing';
import QuickActionsBar from './QuickActionsBar';
import HotLeadsSection from './HotLeadsSection';

interface HomeDashboardProps {
  setView: (view: View) => void;
  setProfileId: (id: string) => void;
}

const ActionTile: React.FC<{ title: string; subtitle: ReactNode; cta: string; icon: ReactNode; onClick?: () => void; }> = ({ title, subtitle, cta, icon, onClick }) => (
  <Card className="flex flex-col" onClick={onClick}>
    <div className="flex-grow">
      <div className="flex items-start gap-4">
        <div className="text-crimson-blue bg-crimson-blue/10 p-3 rounded-lg">
          {icon}
        </div>
        <div>
          <h4 className="font-bold text-base text-text-primary">{title}</h4>
          <p className="text-sm text-text-secondary mt-1">{subtitle}</p>
        </div>
      </div>
    </div>
    <div className="mt-4">
      <Button variant="secondary" size="sm" className="w-full">{cta}</Button>
    </div>
  </Card>
);

const QuickStat: React.FC<{ label: string; value: string; }> = ({ label, value }) => (
    <div className="bg-white p-2 rounded-lg border border-gray-100">
        <p className="text-xs text-text-secondary font-medium">{label}</p>
        <p className="text-lg font-bold text-text-primary mt-0.5">{value}</p>
    </div>
);

const HomeDashboard: React.FC<HomeDashboardProps> = ({ setView, setProfileId }) => {
  const handleViewProfile = () => {
    setProfileId('joseph-banks');
    setView('profile');
  };

  const handleCleanData = () => {
    setView('compliance');
  }

  return (
    <div className="space-y-3">
      {/* Compact Quick Stats Header */}
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-2 p-2 bg-gray-50 rounded-lg border border-gray-200">
        <QuickStat label="Total Donors" value="199,138" />
        <QuickStat label="Pledges Outstanding" value="$220,510" />
        <QuickStat label="Average Gift" value="$87.50" />
        <QuickStat label="% Active Voters" value="78%" />
        <QuickStat label="% Missing Contact" value="12%" />
      </div>

      {/* Compact Real-Time Donation Tracker */}
      <RealTimeDonationTracker />

      {/* Enhanced Quick Actions Bar - Always Visible */}
      <QuickActionsBar />

      {/* Three-Column Layout for Better Space Usage */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-3">
        {/* Column 1: AI Daily Briefing */}
        <div className="lg:col-span-1">
          <AIDailyBriefing setView={setView} setProfileId={setProfileId} />
        </div>

        {/* Column 2: Hot Leads Section */}
        <div className="lg:col-span-1">
          <HotLeadsSection />
        </div>

        {/* Column 3: AI Curated Segments */}
        <div className="lg:col-span-1">
          <Card title="AI Curated Segments">
            <div className="space-y-1.5">
              <div className="flex items-center justify-between p-2 bg-base-200 rounded-lg border border-gray-100 hover:bg-base-300 transition-modern interactive-element">
                <div className="flex items-center gap-2">
                  <div className="p-1 bg-status-info-light rounded-lg">
                    <ArrowPathIcon className="w-3 h-3 text-status-info-dark" />
                  </div>
                  <div>
                    <h5 className="text-hierarchy-6 font-semibold text-text-primary">Comeback Crew</h5>
                    <p className="text-hierarchy-6 text-text-secondary">1,571 • $113K potential</p>
                  </div>
                </div>
                <Button variant="secondary" size="sm" className="text-hierarchy-6 px-2 py-1 btn-modern">View</Button>
              </div>
              <div className="flex items-center justify-between p-2 bg-base-200 rounded-lg border border-gray-100 hover:bg-base-300 transition-modern interactive-element">
                <div className="flex items-center gap-2">
                  <div className="p-1 bg-status-success-light rounded-lg">
                    <MapPinIcon className="w-3 h-3 text-status-success-dark" />
                  </div>
                  <div>
                    <h5 className="text-hierarchy-6 font-semibold text-text-primary">Neighborhood MVPs</h5>
                    <p className="text-hierarchy-6 text-text-secondary">303 • $104K potential</p>
                  </div>
                </div>
                <Button variant="secondary" size="sm" className="text-hierarchy-6 px-2 py-1 btn-modern">View</Button>
              </div>
              <div className="flex items-center justify-between p-2 bg-base-200 rounded-lg border border-gray-100 hover:bg-base-300 transition-modern interactive-element">
                <div className="flex items-center gap-2">
                  <div className="p-1 bg-crimson-blue bg-opacity-20 rounded-lg">
                    <TrendingUpIcon className="w-3 h-3 text-crimson-blue" />
                  </div>
                  <div>
                    <h5 className="text-hierarchy-6 font-semibold text-text-primary">Level-Up List</h5>
                    <p className="text-hierarchy-6 text-text-secondary">578 • $21K potential</p>
                  </div>
                </div>
                <Button variant="secondary" size="sm" className="text-hierarchy-6 px-2 py-1 btn-modern">View</Button>
              </div>
              <Button variant="secondary" className="w-full mt-2 text-hierarchy-6 py-1.5 btn-modern" onClick={() => setView('fundraising')}>
                View All 8 Segments →
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default HomeDashboard;
