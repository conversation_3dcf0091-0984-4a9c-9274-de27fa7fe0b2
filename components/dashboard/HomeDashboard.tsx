import React from 'react';
import { ReactNode } from 'react';
import { View } from '../../types';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { SparklesIcon, LightBulbIcon, CheckCircleIcon, ArrowTrendingUpIcon, UsersIcon, PuzzlePieceIcon } from '../../constants';
import RealTimeDonationTracker from './RealTimeDonationTracker';
import AIDailyBriefing from './AIDailyBriefing';
import QuickActionsBar from './QuickActionsBar';
import HotLeadsSection from './HotLeadsSection';

interface HomeDashboardProps {
  setView: (view: View) => void;
  setProfileId: (id: string) => void;
}

const ActionTile: React.FC<{ title: string; subtitle: ReactNode; cta: string; icon: ReactNode; onClick?: () => void; }> = ({ title, subtitle, cta, icon, onClick }) => (
  <Card className="flex flex-col" onClick={onClick}>
    <div className="flex-grow">
      <div className="flex items-start gap-4">
        <div className="text-crimson-blue bg-crimson-blue/10 p-3 rounded-lg">
          {icon}
        </div>
        <div>
          <h4 className="font-bold text-base text-text-primary">{title}</h4>
          <p className="text-sm text-text-secondary mt-1">{subtitle}</p>
        </div>
      </div>
    </div>
    <div className="mt-4">
      <Button variant="secondary" size="sm" className="w-full">{cta}</Button>
    </div>
  </Card>
);

const QuickStat: React.FC<{ label: string; value: string; }> = ({ label, value }) => (
    <div className="bg-base-100 p-4 rounded-lg shadow-sm">
        <p className="text-sm text-text-secondary">{label}</p>
        <p className="text-2xl font-bold text-text-primary mt-1">{value}</p>
    </div>
);

const HomeDashboard: React.FC<HomeDashboardProps> = ({ setView, setProfileId }) => {
  const handleViewProfile = () => {
    setProfileId('joseph-banks');
    setView('profile');
  };

  const handleCleanData = () => {
    setView('compliance');
  }

  return (
    <div className="space-y-6">
      {/* Compact Quick Stats Header */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 p-4 bg-white rounded-lg border border-gray-200">
        <QuickStat label="Total Donors" value="199,138" />
        <QuickStat label="Pledges Outstanding" value="$220,510" />
        <QuickStat label="Average Gift" value="$87.50" />
        <QuickStat label="% Active Voters" value="78%" />
        <QuickStat label="% Missing Contact" value="12%" />
      </div>

      {/* Hero Section - Real-Time Donation Tracker */}
      <RealTimeDonationTracker />

      {/* AI Daily Briefing - Enhanced with Quick Wins */}
      <AIDailyBriefing />

      {/* Collapsible Quick Actions Bar */}
      <QuickActionsBar />

      {/* Main Content - Focused on Hot Leads */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-3">
          {/* Hot Leads Section - Primary Focus */}
          <HotLeadsSection />
        </div>

        <div className="lg:col-span-1">
          {/* AI Curated Segments - Consolidated */}
          <Card title="AI Curated Segments">
            <div className="space-y-3">
              <div className="p-3 bg-base-200 rounded-lg">
                <h5 className="font-semibold text-sm">🔄 Comeback Crew</h5>
                <p className="text-xs text-text-secondary">1,571 donors • ~$113,000 potential</p>
                <div className="flex gap-1 mt-2">
                  <Button variant="secondary" size="sm" className="flex-1 text-xs">View</Button>
                  <Button variant="secondary" size="sm" className="flex-1 text-xs">Campaign</Button>
                </div>
              </div>
              <div className="p-3 bg-base-200 rounded-lg">
                <h5 className="font-semibold text-sm">🗺️ Neighborhood MVPs</h5>
                <p className="text-xs text-text-secondary">303 donors • ~$104,000 potential</p>
                <div className="flex gap-1 mt-2">
                  <Button variant="secondary" size="sm" className="flex-1 text-xs">View</Button>
                  <Button variant="secondary" size="sm" className="flex-1 text-xs">Campaign</Button>
                </div>
              </div>
              <div className="p-3 bg-base-200 rounded-lg">
                <h5 className="font-semibold text-sm">💡 Level-Up List</h5>
                <p className="text-xs text-text-secondary">578 donors • ~$21,300 potential</p>
                <div className="flex gap-1 mt-2">
                  <Button variant="secondary" size="sm" className="flex-1 text-xs">View</Button>
                  <Button variant="secondary" size="sm" className="flex-1 text-xs">Campaign</Button>
                </div>
              </div>
              <Button variant="secondary" className="w-full mt-3 text-sm" onClick={() => setView('fundraising')}>
                View All 8 Segments →
              </Button>
            </div>
          </Card>
        </div>
      </div>

      {/* Show More Tools - Collapsible Section */}
      <Card className="border-dashed border-2 border-gray-300">
        <div className="text-center">
          <Button
            variant="secondary"
            className="mb-4"
            onClick={() => setView('fundraising')}
          >
            <PuzzlePieceIcon className="w-4 h-4 mr-2" />
            View More Tools & Features
          </Button>
          <p className="text-sm text-text-secondary">
            Access additional AI tools, data cleaning, and advanced segments
          </p>
        </div>
      </Card>
    </div>
  );
};

export default HomeDashboard;
