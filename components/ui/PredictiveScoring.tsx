import React from 'react';
import Card from './Card';
import Button from './Button';
import { SparklesIcon, TrendingUpIcon, TrendingDownIcon } from '../../constants';

interface DonorScore {
  name: string;
  email: string;
  phone: string;
  donationLikelihood: number;
  suggestedAsk: number;
  lastGift: number;
  avgGift: number;
  daysSinceLastGift: number;
  totalGifts: number;
  factors: {
    recency: number;
    frequency: number;
    monetary: number;
    engagement: number;
  };
  insights: string[];
  nextBestAction: string;
}

interface PredictiveScoringProps {
  segmentId: string;
  segmentName: string;
}

const PredictiveScoring: React.FC<PredictiveScoringProps> = ({ segmentId, segmentName }) => {
  const mockScores: DonorScore[] = [
    {
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '(*************',
      donationLikelihood: 92,
      suggestedAsk: 1500,
      lastGift: 1200,
      avgGift: 800,
      daysSinceLastGift: 45,
      totalGifts: 8,
      factors: {
        recency: 85,
        frequency: 90,
        monetary: 95,
        engagement: 88
      },
      insights: [
        'Consistent major donor with increasing gift sizes',
        'High email engagement (85% open rate)',
        'Lives in high-capacity ZIP code',
        'Responds well to personal calls'
      ],
      nextBestAction: 'Personal call with specific project proposal'
    },
    {
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '(*************',
      donationLikelihood: 78,
      suggestedAsk: 350,
      lastGift: 250,
      avgGift: 180,
      daysSinceLastGift: 180,
      totalGifts: 12,
      factors: {
        recency: 45,
        frequency: 85,
        monetary: 70,
        engagement: 92
      },
      insights: [
        'Lapsed but historically loyal donor',
        'High social media engagement',
        'Prefers email communication',
        'Responds to emotional appeals'
      ],
      nextBestAction: 'Reactivation email with impact story'
    },
    {
      name: 'Michael Chen',
      email: '<EMAIL>',
      phone: '(*************',
      donationLikelihood: 65,
      suggestedAsk: 200,
      lastGift: 150,
      avgGift: 120,
      daysSinceLastGift: 90,
      totalGifts: 6,
      factors: {
        recency: 60,
        frequency: 55,
        monetary: 50,
        engagement: 75
      },
      insights: [
        'Growing donor with potential',
        'Young professional demographic',
        'Prefers online giving',
        'Responds to peer-to-peer campaigns'
      ],
      nextBestAction: 'Peer-to-peer campaign invitation'
    }
  ];

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-100';
    if (score >= 60) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getScoreIcon = (score: number) => {
    if (score >= 70) return <TrendingUpIcon className="w-4 h-4" />;
    return <TrendingDownIcon className="w-4 h-4" />;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  return (
    <Card title="🎯 AI Predictive Scoring" className="mt-6">
      <div className="mb-4">
        <p className="text-sm text-gray-600">
          AI-powered likelihood scores for {segmentName} donors based on giving history, engagement, and behavioral patterns.
        </p>
      </div>

      <div className="space-y-4">
        {mockScores.map((donor, index) => (
          <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h4 className="font-semibold text-lg">{donor.name}</h4>
                  <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-sm font-medium ${getScoreColor(donor.donationLikelihood)}`}>
                    {getScoreIcon(donor.donationLikelihood)}
                    {donor.donationLikelihood}% likely to give
                  </div>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-3">
                  <div>
                    <span className="font-medium">Suggested Ask:</span>
                    <p className="text-green-600 font-bold">{formatCurrency(donor.suggestedAsk)}</p>
                  </div>
                  <div>
                    <span className="font-medium">Last Gift:</span>
                    <p>{formatCurrency(donor.lastGift)}</p>
                  </div>
                  <div>
                    <span className="font-medium">Avg Gift:</span>
                    <p>{formatCurrency(donor.avgGift)}</p>
                  </div>
                  <div>
                    <span className="font-medium">Days Since Last:</span>
                    <p>{donor.daysSinceLastGift} days</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              {Object.entries(donor.factors).map(([factor, score]) => (
                <div key={factor} className="text-center">
                  <div className="text-xs text-gray-500 uppercase tracking-wide mb-1">
                    {factor}
                  </div>
                  <div className={`text-sm font-bold ${getScoreColor(score).split(' ')[0]}`}>
                    {score}%
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                    <div
                      className={`h-2 rounded-full ${score >= 80 ? 'bg-green-500' : score >= 60 ? 'bg-yellow-500' : 'bg-red-500'}`}
                      style={{ width: `${score}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mb-4">
              <h5 className="font-medium text-sm mb-2 flex items-center gap-1">
                <SparklesIcon className="w-4 h-4 text-purple-500" />
                AI Insights
              </h5>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {donor.insights.map((insight, idx) => (
                  <div key={idx} className="text-xs text-gray-600 bg-gray-50 rounded px-2 py-1">
                    • {insight}
                  </div>
                ))}
              </div>
            </div>

            <div className="flex items-center justify-between pt-3 border-t border-gray-100">
              <div className="flex-1">
                <span className="text-sm font-medium text-purple-600">Next Best Action:</span>
                <p className="text-sm text-gray-700">{donor.nextBestAction}</p>
              </div>
              <div className="flex gap-2 ml-4">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => window.open(`tel:${donor.phone}`, '_self')}
                >
                  📞 Call
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => window.open(`mailto:${donor.email}`, '_self')}
                >
                  📧 Email
                </Button>
                <Button
                  size="sm"
                  onClick={() => console.log(`Generate script for ${donor.name}`)}
                >
                  📝 Script
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            <strong>Segment Summary:</strong> Avg likelihood: 78% • Total potential: {formatCurrency(2050)} • 3 high-priority contacts
          </div>
          <div className="flex gap-2">
            <Button variant="secondary" size="sm">
              📊 Export Scores
            </Button>
            <Button size="sm">
              🚀 Bulk Action
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default PredictiveScoring;
